<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Database Card Component -->
    <t t-name="components.database_card">
        <div class="group relative bg-white border border-gray-200 rounded-lg overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-[1.01] hover:border-blue-300 card-hover"
             onclick="databaseOps.connectToDatabase(this.dataset.dbname)"
             t-att-data-dbname="db['name']">
            
            <!-- Subtle gradient overlay on hover -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-50/0 to-indigo-50/0 group-hover:from-blue-50/20 group-hover:to-indigo-50/20 transition-all duration-300 pointer-events-none"></div>
            
            <!-- Card Header -->
            <div class="relative p-4 sm:p-5 border-b border-gray-100">
                <div class="flex items-start justify-between">
                    <div class="flex-1 min-w-0">
                        <h3 class="text-base sm:text-lg font-semibold text-gray-900 truncate group-hover:text-blue-700 transition-colors duration-300" t-esc="db['name']"/>
                        <div class="mt-1 flex items-center space-x-2">
                            <t t-call="components.status_badge">
                                <t t-set="status" t-value="db.get('status', 'unknown')"/>
                                <t t-set="is_initialized" t-value="db.get('is_initialized', False)"/>
                            </t>
                        </div>
                    </div>
                    
                    <!-- Database Actions Menu -->
                    <div class="flex-shrink-0 ml-2">
                        <button class="p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-1 focus:ring-blue-500" 
                                onclick="event.stopPropagation(); toggleDatabaseMenu(this)" 
                                type="button">
                            <i class="fas fa-ellipsis-v text-sm"></i>
                        </button>
                        
                        <!-- Dropdown Menu -->
                        <div class="hidden absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10" data-menu="true">
                            <div class="py-1">
                                <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                                        onclick="event.stopPropagation(); databaseOps.connectToDatabase(this.closest('[data-dbname]').dataset.dbname)">
                                    <i class="fas fa-sign-in-alt text-green-500"></i>
                                    <span>Connect</span>
                                </button>
                                <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                                        onclick="event.stopPropagation(); showDatabaseInfo(this.closest('[data-dbname]').dataset.dbname)">
                                    <i class="fas fa-info-circle text-blue-500"></i>
                                    <span>Info</span>
                                </button>
                                <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                                        onclick="event.stopPropagation(); databaseOps.duplicateDatabase(this.closest('[data-dbname]').dataset.dbname)">
                                    <i class="fas fa-copy text-green-500"></i>
                                    <span>Duplicate</span>
                                </button>
                                <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2"
                                        onclick="event.stopPropagation(); databaseOps.backupDatabase(this.closest('[data-dbname]').dataset.dbname)">
                                    <i class="fas fa-download text-indigo-500"></i>
                                    <span>Backup</span>
                                </button>
                                <div class="border-t border-gray-100"></div>
                                <button class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                                        onclick="event.stopPropagation(); databaseOps.deleteDatabase(this.closest('[data-dbname]').dataset.dbname)">
                                    <i class="fas fa-trash text-red-500"></i>
                                    <span>Delete</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Card Body -->
            <div class="relative p-4 sm:p-5">
                <!-- Database Info Grid -->
                <div class="grid grid-cols-2 gap-3 mb-4 text-xs sm:text-sm">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-calendar-alt text-gray-400 text-xs"></i>
                        <span class="text-gray-600">Created</span>
                    </div>
                    <div class="text-gray-900 font-medium text-right">
                        <span t-esc="db.get('created_display', db.get('created_date', 'Unknown'))"/>
                    </div>

                    <div class="flex items-center space-x-2">
                        <i class="fas fa-hdd text-gray-400 text-xs"></i>
                        <span class="text-gray-600">Size</span>
                    </div>
                    <div class="text-gray-900 font-medium text-right">
                        <span t-esc="db.get('size', 'Unknown')"/>
                    </div>

                    <div class="flex items-center space-x-2">
                        <i class="fas fa-table text-gray-400 text-xs"></i>
                        <span class="text-gray-600">Tables</span>
                    </div>
                    <div class="text-gray-900 font-medium text-right">
                        <span t-esc="db.get('table_count', '0')"/>
                    </div>

                    <div class="flex items-center space-x-2">
                        <i class="fas fa-plug text-gray-400 text-xs"></i>
                        <span class="text-gray-600">Connections</span>
                    </div>
                    <div class="text-gray-900 font-medium text-right">
                        <span t-esc="db.get('active_connections', '0')"/>
                    </div>
                </div>

                <!-- Memory Registry Status -->
                <t t-if="db.get('has_memory_registry', False)">
                    <div class="mb-3 p-2 bg-green-50 border border-green-200 rounded-md">
                        <div class="flex items-center space-x-2 text-xs">
                            <i class="fas fa-memory text-green-600"></i>
                            <span class="text-green-800 font-medium">Memory Registry Active</span>
                        </div>
                        <t t-if="db.get('registry_info')">
                            <div class="mt-1 text-xs text-green-700">
                                <span t-esc="'Modules: ' + str(db['registry_info'].get('installed_modules_count', 0))"/>
                                <span class="mx-1">•</span>
                                <span t-esc="'Routes: ' + str(db['registry_info'].get('routes_count', 0))"/>
                            </div>
                        </t>
                    </div>
                </t>
                
                <!-- Action Button -->
                <div class="mt-4">
                    <t t-if="db.get('is_initialized', False)">
                        <button class="w-full px-3 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-md text-sm font-medium hover:from-green-600 hover:to-emerald-600 focus:outline-none focus:ring-1 focus:ring-green-500 transition-all duration-300 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md"
                                onclick="databaseOps.connectToDatabase(this.dataset.dbname)"
                                t-att-data-dbname="db['name']">
                            <i class="fas fa-sign-in-alt text-sm"></i>
                            <span>Connect</span>
                            <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                        </button>
                    </t>
                    <t t-else="">
                        <button class="w-full px-3 py-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-md text-sm font-medium hover:from-yellow-600 hover:to-orange-600 focus:outline-none focus:ring-1 focus:ring-yellow-500 transition-all duration-300 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md"
                                onclick="databaseOps.initializeDatabase(this.dataset.dbname)"
                                t-att-data-dbname="db['name']">
                            <i class="fas fa-cog text-sm"></i>
                            <span>Initialize</span>
                            <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                        </button>
                    </t>
                </div>
            </div>
        </div>
    </t>

    <!-- Status Badge Component -->
    <t t-name="components.status_badge">
        <t t-if="is_initialized">
            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                <i class="fas fa-check-circle mr-1 text-xs"></i>
                Ready
            </span>
        </t>
        <t t-else="">
            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                <i class="fas fa-exclamation-triangle mr-1 text-xs"></i>
                Not Initialized
            </span>
        </t>
    </t>
</templates>
