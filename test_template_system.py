#!/usr/bin/env python3
"""
Test the template system to see if it's working
"""
import asyncio
from erp.templates.manager import get_template_manager

async def test_template_system():
    """Test if the template system is working"""
    template_manager = get_template_manager()
    
    print("Template manager created")
    print(f"Template directories: {template_manager.template_dirs}")
    
    # Try to load a simple template
    try:
        print("Loading base_layout template...")
        template_manager.load_template_file("layouts/base_layout.xml")
        print("✅ base_layout loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load base_layout: {e}")
        return
    
    # Load all required templates
    template_files = [
        "layouts/base_layout.xml",
        "styles/tailwind_config.xml",
        "components/database_card.xml",
        "components/status_bar.xml",
        "components/create_database_modal.xml",
        "components/empty_state.xml",
        "scripts/modal_manager.js.xml",
        "scripts/database_operations.js.xml",
        "scripts/search_filter.js.xml",
        "scripts/utils.js.xml",
        "database_list_enhanced_modular.xml"
    ]

    for template_file in template_files:
        try:
            print(f"Loading {template_file}...")
            template_manager.load_template_file(template_file)
            print(f"✅ {template_file} loaded successfully")
        except Exception as e:
            print(f"❌ Failed to load {template_file}: {e}")
            return
    
    # List all loaded templates
    templates = template_manager.list_templates()
    print(f"Loaded templates: {templates}")
    
    # Try to render a simple test template first
    try:
        print("Loading simple test template...")
        template_manager.load_template_file("test_simple_template.xml")
        print("✅ Simple test template loaded")

        context = {"title": "Test Page"}
        print("Attempting to render simple test template...")
        result = template_manager.render_template("test_simple", context)
        print(f"✅ Simple template rendered successfully, length: {len(result)}")
        if len(result) < 200:
            print(f"Result: {result}")
        else:
            print(f"Result preview: {result[:200]}...")
    except Exception as e:
        print(f"❌ Failed to render simple template: {e}")
        import traceback
        traceback.print_exc()
        return

    # Try to render the complex template
    try:
        context = {
            "title": "Test Page",
            "databases": [],
            "config": {"is_development_mode": True}
        }
        print("Attempting to render complex template...")
        result = template_manager.render_template("database_list_enhanced_modular", context)
        print(f"✅ Complex template rendered successfully, length: {len(result)}")
        if len(result) < 100:
            print(f"Result: {result}")
        else:
            print(f"Result preview: {result[:200]}...")
    except Exception as e:
        print(f"❌ Failed to render complex template: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_template_system())
