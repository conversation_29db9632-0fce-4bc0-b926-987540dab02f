<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Empty State Component -->
    <t t-name="components.empty_state">
        <div class="text-center py-24 bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/30 border-2 border-dashed border-gray-300 rounded-3xl shadow-inner">
            <div class="relative mx-auto mb-8">
                <!-- Animated background circles -->
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="w-32 h-32 bg-blue-100 rounded-full animate-pulse opacity-20"></div>
                </div>
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="w-24 h-24 bg-indigo-200 rounded-full animate-ping opacity-10"></div>
                </div>
                
                <!-- Main icon -->
                <div class="relative w-20 h-20 mx-auto bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-database text-white text-2xl animate-bounce-gentle"></i>
                </div>
            </div>
            
            <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-4">No Databases Found</h3>
            <p class="text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
                Get started by creating your first database. You can set up a new database with demo data or start with a clean slate.
            </p>
            
            <div class="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                <button class="inline-flex items-center px-6 py-3 rounded-xl text-base font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 shadow-lg hover:shadow-xl" 
                        onclick="showCreateDatabaseModal()">
                    <i class="fas fa-plus mr-2"></i>
                    Create Database
                </button>
                
                <button class="inline-flex items-center px-6 py-3 rounded-xl text-base font-medium text-gray-700 bg-white border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-300 shadow-lg hover:shadow-xl"
                        onclick="databaseOps.refreshDatabases()">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh List
                </button>
            </div>
        </div>
    </t>

    <!-- Loading State Component -->
    <t t-name="components.loading_state">
        <div class="text-center py-24">
            <div class="relative mx-auto mb-8 w-20 h-20">
                <!-- Spinning loader -->
                <div class="absolute inset-0 border-4 border-gray-200 rounded-full"></div>
                <div class="absolute inset-0 border-4 border-blue-600 rounded-full border-t-transparent animate-spin"></div>
                
                <!-- Center icon -->
                <div class="absolute inset-0 flex items-center justify-center">
                    <i class="fas fa-database text-blue-600 text-lg"></i>
                </div>
            </div>
            
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Loading Databases</h3>
            <p class="text-gray-600">Please wait while we fetch your databases...</p>
        </div>
    </t>

    <!-- Error State Component -->
    <t t-name="components.error_state">
        <div class="text-center py-24">
            <div class="relative mx-auto mb-8 w-20 h-20 bg-red-100 rounded-2xl flex items-center justify-center">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Error Loading Databases</h3>
            <p class="text-gray-600 mb-6" t-esc="error_message or 'An unexpected error occurred while loading the database list.'"/>
            
            <div class="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                <button class="inline-flex items-center px-6 py-3 rounded-xl text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300"
                        onclick="databaseOps.refreshDatabases()">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Try Again
                </button>
                
                <button class="inline-flex items-center px-6 py-3 rounded-xl text-base font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-300" 
                        onclick="showCreateDatabaseModal()">
                    <i class="fas fa-plus mr-2"></i>
                    Create New Database
                </button>
            </div>
        </div>
    </t>
</templates>
