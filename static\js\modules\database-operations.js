/**
 * Database Operations Module
 * Handles CRUD operations for databases, API calls, and loading states
 */

class DatabaseOperations {
    constructor() {
        this.apiBaseUrl = '/api/databases';
        this.isLoading = false;
    }

    // Show loading state on a card
    setCardLoading(dbName, isLoading, message = 'Loading...') {
        const card = document.querySelector(`[data-dbname="${dbName}"]`);
        if (!card) return;

        const button = card.querySelector('button');
        if (!button) return;

        if (isLoading) {
            button.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>${message}`;
            button.disabled = true;
            button.classList.add('opacity-75');
        } else {
            button.disabled = false;
            button.classList.remove('opacity-75');
            // Restore original button content based on database state
            const isInitialized = card.querySelector('.bg-green-100');
            if (isInitialized) {
                button.innerHTML = '<i class="fas fa-sign-in-alt text-sm"></i><span>Connect</span><i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>';
            } else {
                button.innerHTML = '<i class="fas fa-cog text-sm"></i><span>Initialize</span><i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>';
            }
        }
    }

    // Create a new database
    async handleCreateDatabase(event) {
        event.preventDefault();

        if (this.isLoading) return;
        this.isLoading = true;

        const formData = new FormData(event.target);
        const data = {
            name: formData.get('name'),
            language: formData.get('language'),
            demo: formData.has('demo')
        };

        // Validate database name
        if (!data.name || !data.name.match(/^[a-zA-Z0-9_-]+$/)) {
            alert('Database name must contain only letters, numbers, underscores, and hyphens.');
            this.isLoading = false;
            return;
        }

        try {
            const response = await fetch(this.apiBaseUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                hideCreateDatabaseModal();
                this.showSuccessMessage('Database created successfully! Redirecting...');
                
                // Redirect to the new database
                setTimeout(() => {
                    window.location.href = '/home?db=' + encodeURIComponent(data.name);
                }, 1500);
            } else {
                throw new Error(result.detail || result.message || 'Failed to create database');
            }
        } catch (error) {
            console.error('Error creating database:', error);
            this.showErrorMessage('Failed to create database: ' + error.message);
        } finally {
            this.isLoading = false;
        }
    }

    // Connect to a database
    connectToDatabase(dbName) {
        if (this.isLoading) return;
        
        this.setCardLoading(dbName, true, 'Connecting...');
        
        // Navigate to the database
        window.location.href = '/home?db=' + encodeURIComponent(dbName);
    }

    // Initialize a database
    initializeDatabase(dbName) {
        if (this.isLoading) return;

        const confirmed = confirm('This will initialize the database with the base module. This process may take a few minutes. Continue?');
        if (!confirmed) return;

        this.setCardLoading(dbName, true, 'Initializing...');
        
        // Show initialization message
        alert('Database initialization will begin when you connect. Please wait for the process to complete.');
        
        // Navigate to trigger initialization
        window.location.href = '/home?db=' + encodeURIComponent(dbName);
    }

    // Delete a database
    async deleteDatabase(dbName) {
        if (this.isLoading) return;

        const confirmed = confirm(`Are you sure you want to delete database "${dbName}"? This action cannot be undone.`);
        if (!confirmed) return;

        const confirmText = prompt('This will permanently delete all data in the database. Type "DELETE" to confirm:');
        if (confirmText !== 'DELETE') {
            if (confirmText !== null) {
                alert('Deletion cancelled. You must type "DELETE" exactly to confirm.');
            }
            return;
        }

        this.isLoading = true;
        this.setCardLoading(dbName, true, 'Deleting...');

        try {
            const response = await fetch(`${this.apiBaseUrl}/${encodeURIComponent(dbName)}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showSuccessMessage('Database deleted successfully');
                
                // Remove the card from the UI
                const card = document.querySelector(`[data-dbname="${dbName}"]`);
                if (card) {
                    card.style.transition = 'all 0.3s ease-out';
                    card.style.transform = 'scale(0.95)';
                    card.style.opacity = '0';
                    setTimeout(() => {
                        card.remove();
                        this.updateDatabaseCount();
                    }, 300);
                }
            } else {
                throw new Error(result.detail || result.message || 'Failed to delete database');
            }
        } catch (error) {
            console.error('Error deleting database:', error);
            this.showErrorMessage('Failed to delete database: ' + error.message);
            this.setCardLoading(dbName, false);
        } finally {
            this.isLoading = false;
        }
    }

    // Duplicate a database
    async duplicateDatabase(dbName) {
        if (this.isLoading) return;

        const newName = prompt(`Enter name for the duplicate of "${dbName}":`);
        if (!newName || newName.trim() === '') return;

        const cleanName = newName.trim().toLowerCase();
        if (cleanName === dbName) {
            this.showErrorMessage('Duplicate name cannot be the same as original database');
            return;
        }

        this.isLoading = true;
        this.setCardLoading(dbName, true, 'Duplicating...');

        try {
            const response = await fetch(`${this.apiBaseUrl}/${encodeURIComponent(dbName)}/duplicate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ new_name: cleanName })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showSuccessMessage(`Database duplicated successfully as "${cleanName}"!`);
                // Refresh the page to show the new database
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                throw new Error(result.detail || result.message || 'Failed to duplicate database');
            }
        } catch (error) {
            console.error('Error duplicating database:', error);
            this.showErrorMessage('Failed to duplicate database: ' + error.message);
        } finally {
            this.setCardLoading(dbName, false);
            this.isLoading = false;
        }
    }

    // Backup a database
    async backupDatabase(dbName) {
        if (this.isLoading) return;

        this.isLoading = true;
        this.setCardLoading(dbName, true, 'Creating backup...');

        try {
            const response = await fetch(`${this.apiBaseUrl}/${encodeURIComponent(dbName)}/backup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                // Handle file download
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${dbName}_backup_${new Date().toISOString().split('T')[0]}.sql`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                this.showSuccessMessage('Database backup downloaded successfully!');
            } else {
                const result = await response.json();
                throw new Error(result.detail || result.message || 'Failed to create backup');
            }
        } catch (error) {
            console.error('Error backing up database:', error);
            this.showErrorMessage('Failed to backup database: ' + error.message);
        } finally {
            this.setCardLoading(dbName, false);
            this.isLoading = false;
        }
    }

    // Refresh the database list
    refreshDatabases() {
        window.location.reload();
    }

    // Refresh system statistics
    refreshSystemStats() {
        // Update statistics counters
        this.updateSystemStatistics();
        this.showSuccessMessage('Statistics refreshed');
    }

    // Update system statistics
    updateSystemStatistics() {
        const cards = document.querySelectorAll('[data-dbname]');
        const totalDatabases = cards.length;

        let totalConnections = 0;
        let totalTables = 0;
        let memoryRegistries = 0;
        let initializedCount = 0;

        cards.forEach(card => {
            // Extract data from card elements
            const connectionText = card.querySelector('.fa-plug')?.parentElement?.nextElementSibling?.textContent || '0';
            const tableText = card.querySelector('.fa-table')?.parentElement?.nextElementSibling?.textContent || '0';
            const hasRegistry = card.querySelector('.bg-green-50') !== null;
            const isInitialized = card.querySelector('.bg-green-100') !== null;

            totalConnections += parseInt(connectionText) || 0;
            totalTables += parseInt(tableText) || 0;
            if (hasRegistry) memoryRegistries++;
            if (isInitialized) initializedCount++;
        });

        // Update DOM elements
        const totalDbElement = document.getElementById('total-databases');
        const totalConnElement = document.getElementById('total-connections');
        const totalTablesElement = document.getElementById('total-tables');
        const memoryRegElement = document.getElementById('memory-registries');

        if (totalDbElement) totalDbElement.textContent = totalDatabases;
        if (totalConnElement) totalConnElement.textContent = totalConnections;
        if (totalTablesElement) totalTablesElement.textContent = totalTables;
        if (memoryRegElement) memoryRegElement.textContent = memoryRegistries;

        // Update progress bar
        const progressBar = document.querySelector('.bg-green-500.h-2');
        const progressText = document.querySelector('.text-xs.text-gray-600 span:last-child');

        if (progressBar && totalDatabases > 0) {
            const percentage = (initializedCount / totalDatabases) * 100;
            progressBar.style.width = percentage + '%';
            if (progressText) {
                progressText.textContent = Math.round(percentage * 10) / 10 + '%';
            }
        }
    }

    // Export system report
    exportSystemReport() {
        const cards = document.querySelectorAll('[data-dbname]');
        const report = {
            timestamp: new Date().toISOString(),
            system: {
                total_databases: cards.length,
                mode: document.querySelector('.bg-green-100, .bg-yellow-100')?.textContent?.includes('Multi-DB') ? 'Multi-DB' : 'Single-DB'
            },
            databases: []
        };

        cards.forEach(card => {
            const dbName = card.dataset.dbname;
            const connectionText = card.querySelector('.fa-plug')?.parentElement?.nextElementSibling?.textContent || '0';
            const tableText = card.querySelector('.fa-table')?.parentElement?.nextElementSibling?.textContent || '0';
            const hasRegistry = card.querySelector('.bg-green-50') !== null;
            const isInitialized = card.querySelector('.bg-green-100') !== null;

            report.databases.push({
                name: dbName,
                connections: parseInt(connectionText) || 0,
                tables: parseInt(tableText) || 0,
                has_memory_registry: hasRegistry,
                is_initialized: isInitialized
            });
        });

        // Download as JSON
        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system_report_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        this.showSuccessMessage('System report exported successfully');
    }

    // Update database count in status bar
    updateDatabaseCount() {
        const cards = document.querySelectorAll('[data-dbname]');
        const count = cards.length;
        const countBadge = document.querySelector('.bg-blue-100.text-blue-800 span');
        if (countBadge) {
            countBadge.textContent = `${count} Database${count === 1 ? '' : 's'}`;
        }

        // Show empty state if no databases
        if (count === 0) {
            const databaseList = document.getElementById('database-list');
            if (databaseList) {
                databaseList.innerHTML = `
                    <div class="text-center py-24 bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/30 border-2 border-dashed border-gray-300 rounded-3xl shadow-inner">
                        <div class="relative mx-auto mb-8">
                            <div class="relative w-20 h-20 mx-auto bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-database text-white text-2xl"></i>
                            </div>
                        </div>
                        <h3 class="text-xl sm:text-2xl font-bold text-gray-900 mb-4">No Databases Found</h3>
                        <p class="text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
                            Get started by creating your first database. You can set up a new database with demo data or start with a clean slate.
                        </p>
                        <div class="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4">
                            <button class="inline-flex items-center px-6 py-3 rounded-xl text-base font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 shadow-lg hover:shadow-xl" onclick="showCreateDatabaseModal()">
                                <i class="fas fa-plus mr-2"></i>
                                Create Database
                            </button>
                            <button class="inline-flex items-center px-6 py-3 rounded-xl text-base font-medium text-gray-700 bg-white border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-300 shadow-lg hover:shadow-xl" onclick="databaseOps.refreshDatabases()">
                                <i class="fas fa-sync-alt mr-2"></i>
                                Refresh List
                            </button>
                        </div>
                    </div>
                `;
            }
        }
    }

    // Show success message
    showSuccessMessage(message) {
        if (window.Utils && window.Utils.showToast) {
            window.Utils.showToast(message, 'success');
        } else {
            alert(message);
        }
    }

    // Show error message
    showErrorMessage(message) {
        if (window.Utils && window.Utils.showToast) {
            window.Utils.showToast(message, 'error');
        } else {
            alert(message);
        }
    }
}

// Global database operations instance
const databaseOps = new DatabaseOperations();

// Make database operations available globally
window.databaseOps = databaseOps;
