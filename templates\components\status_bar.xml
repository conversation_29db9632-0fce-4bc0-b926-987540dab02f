<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Status Bar Component -->
    <t t-name="components.status_bar">
        <div class="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6 shadow-sm">
            <!-- Top Row: System Status and Search -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 mb-3">
                <div class="flex items-center space-x-2 sm:space-x-3">
                    <t t-call="components.system_mode_badge">
                        <t t-set="is_multi_db" t-value="config.is_multi_db_mode"/>
                    </t>
                    
                    <t t-call="components.database_count_badge">
                        <t t-set="count" t-value="len(databases) if databases else 0"/>
                    </t>
                </div>
                
                <!-- Search Bar -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400 text-sm"></i>
                        </div>
                        <input type="text" 
                               id="database-search" 
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors" 
                               placeholder="Search databases..." 
                               onkeyup="filterDatabases()"
                               autocomplete="off"/>
                    </div>
                </div>
            </div>
            
            <!-- Bottom Row: Environment Info -->
            <div class="flex flex-wrap items-center gap-2 text-xs text-gray-600">
                <t t-call="components.environment_info">
                    <t t-set="environment" t-value="config"/>
                </t>
            </div>
        </div>
    </t>

    <!-- System Mode Badge -->
    <t t-name="components.system_mode_badge">
        <div t-att-class="'inline-flex items-center px-2 py-1 rounded-md text-xs font-medium ' + ('bg-green-100 text-green-800 border border-green-200' if is_multi_db else 'bg-yellow-100 text-yellow-800 border border-yellow-200')">
            <i t-att-class="'fas mr-1 text-xs ' + ('fa-layer-group' if is_multi_db else 'fa-database')"></i>
            <span t-esc="'Multi-DB' if is_multi_db else 'Single-DB'"/>
        </div>
    </t>

    <!-- Database Count Badge -->
    <t t-name="components.database_count_badge">
        <div class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
            <i class="fas fa-database mr-1 text-xs"></i>
            <span t-esc="str(count) + (' Database' if count == 1 else ' Databases')"/>
        </div>
    </t>

    <!-- Environment Information -->
    <t t-name="components.environment_info">
        <div class="flex items-center space-x-1">
            <i class="fas fa-server text-gray-400"></i>
            <span>Host:</span>
            <span class="font-medium" t-esc="environment.get('host', 'localhost')"/>
        </div>
        
        <div class="flex items-center space-x-1">
            <i class="fas fa-plug text-gray-400"></i>
            <span>Port:</span>
            <span class="font-medium" t-esc="environment.get('port', '8000')"/>
        </div>
        
        <t t-if="environment.get('database_url')">
            <div class="flex items-center space-x-1">
                <i class="fas fa-link text-gray-400"></i>
                <span>DB:</span>
                <span class="font-medium">Connected</span>
            </div>
        </t>
        
        <div class="flex items-center space-x-1">
            <i class="fas fa-clock text-gray-400"></i>
            <span>Updated:</span>
            <span class="font-medium" id="last-updated">Just now</span>
        </div>
    </t>
</templates>
