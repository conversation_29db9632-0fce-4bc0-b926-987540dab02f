<?xml version="1.0" encoding="utf-8"?>
<templates>
    <!-- Tailwind CSS Configuration -->
    <t t-name="styles.tailwind_config">
        <script>
            tailwind.config = {
                theme: {
                    extend: {
                        fontFamily: {
                            'sans': ['Inter', 'system-ui', 'sans-serif'],
                        },
                        colors: {
                            'primary': {
                                50: '#eff6ff',
                                500: '#3b82f6',
                                600: '#2563eb',
                                700: '#1d4ed8',
                            }
                        },
                        animation: {
                            'fade-in': 'fadeIn 0.5s ease-in-out',
                            'slide-up': 'slideUp 0.3s ease-out',
                            'bounce-gentle': 'bounceGentle 2s infinite',
                        },
                        keyframes: {
                            fadeIn: {
                                '0%': { opacity: '0' },
                                '100%': { opacity: '1' },
                            },
                            slideUp: {
                                '0%': { transform: 'translateY(10px)', opacity: '0' },
                                '100%': { transform: 'translateY(0)', opacity: '1' },
                            },
                            bounceGentle: {
                                '0%, 100%': { transform: 'translateY(0)' },
                                '50%': { transform: 'translateY(-5px)' },
                            }
                        }
                    }
                }
            }
        </script>
    </t>

    <!-- Custom CSS Styles -->
    <t t-name="styles.custom_css">
        <style>
            /* Custom scrollbar styles */
            ::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }
            
            ::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 3px;
            }
            
            ::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 3px;
            }
            
            ::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }

            /* Smooth transitions for interactive elements */
            .transition-all {
                transition-property: all;
                transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
                transition-duration: 300ms;
            }

            /* Focus styles for accessibility */
            .focus-ring:focus {
                outline: 2px solid #3b82f6;
                outline-offset: 2px;
            }

            /* Card hover effects */
            .card-hover:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            }

            /* Loading spinner animation */
            @keyframes spin {
                to {
                    transform: rotate(360deg);
                }
            }
            
            .animate-spin {
                animation: spin 1s linear infinite;
            }

            /* Modal backdrop blur */
            .backdrop-blur-sm {
                backdrop-filter: blur(4px);
            }
        </style>
    </t>
</templates>
