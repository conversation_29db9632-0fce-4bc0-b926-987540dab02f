"""
Database list viewing routes - only list viewing and redirection
"""
from fastapi import API<PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import HTMLResponse
from typing import Dict, Any, List
import re

from ..config import config
from ..database.registry import DatabaseRegistry
from ..templates.manager import get_template_manager

# Only view router for database listing - no API management routes
view_router = APIRouter(prefix="/databases", tags=["database-views"])


async def get_database_list() -> List[Dict[str, Any]]:
    """Helper function to get database list with information"""
    from ..database.memory import DatabaseFilterProcessor, MemoryRegistryManager
    
    db_info_list = []

    # Get all available databases
    databases = await DatabaseRegistry.list_databases()
    
    # Process database filtering using the new system
    filter_result = await DatabaseFilterProcessor.process_database_filter(
        databases=databases,
        db_filter=config.db_filter,
        specific_db_name=config.get_default_database()
    )
    
    filtered_databases = filter_result['filtered_databases']
    
    # If memory registry should be created, ensure it exists
    if filter_result['should_create_registry'] and filter_result['registry_db_name']:
        registry_db = filter_result['registry_db_name']
        await MemoryRegistryManager.get_registry(registry_db)
        print(f"[INFO] Memory registry ensured for database: {registry_db}")

    # Build database info for filtered databases
    for db_name in filtered_databases:
        # Check if this database has a memory registry
        has_registry = await MemoryRegistryManager.has_registry(db_name)
        
        # Check if base module is installed
        base_module_installed = await MemoryRegistryManager._is_base_module_installed(db_name)
        
        # Determine database initialization status
        if base_module_installed:
            init_status = "ready"
            init_message = "Base module installed - Ready for use"
            init_icon = "fa-check-circle"
            init_color = "green"
        else:
            init_status = "needs_init"
            init_message = "Requires base module installation"
            init_icon = "fa-exclamation-triangle"
            init_color = "yellow"
        
        # Get comprehensive database information
        try:
            db_manager = await DatabaseRegistry.get_database('postgres')

            # Get database size
            size_query = f"SELECT pg_size_pretty(pg_database_size('{db_name}')) as size"
            size_result = await db_manager.fetch(size_query)
            size = size_result[0]['size'] if size_result else 'Unknown'

            # Get creation date (approximate)
            created_query = f"""
                SELECT (pg_stat_file('base/'||oid||'/PG_VERSION')).modification as created
                FROM pg_database WHERE datname = '{db_name}'
            """
            created_result = await db_manager.fetch(created_query)
            created = created_result[0]['created'].isoformat() if created_result else None

            # Get database statistics
            stats_query = f"""
                SELECT
                    d.datname as name,
                    pg_catalog.pg_get_userbyid(d.datdba) as owner,
                    pg_encoding_to_char(d.encoding) as encoding,
                    d.datcollate as collate,
                    d.datctype as ctype,
                    CASE WHEN d.datallowconn THEN 'Yes' ELSE 'No' END as allow_connections,
                    d.datconnlimit as connection_limit,
                    pg_size_pretty(pg_database_size(d.datname)) as size_pretty,
                    pg_database_size(d.datname) as size_bytes
                FROM pg_database d
                WHERE d.datname = '{db_name}'
            """
            stats_result = await db_manager.fetch(stats_query)
            db_stats = stats_result[0] if stats_result else {}

            # Get connection count
            conn_query = f"""
                SELECT count(*) as active_connections
                FROM pg_stat_activity
                WHERE datname = '{db_name}' AND state = 'active'
            """
            conn_result = await db_manager.fetch(conn_query)
            active_connections = conn_result[0]['active_connections'] if conn_result else 0

            # Get table count
            table_query = f"""
                SELECT count(*) as table_count
                FROM information_schema.tables
                WHERE table_catalog = '{db_name}'
                AND table_schema NOT IN ('information_schema', 'pg_catalog')
            """
            table_result = await db_manager.fetch(table_query)
            table_count = table_result[0]['table_count'] if table_result else 0

        except Exception as e:
            # Use logger for consistency
            from erp.logging import get_logger
            logger = get_logger(__name__)
            logger.debug(f"Error getting info for database {db_name}: {e}")
            size = 'Unknown'
            created = None
            db_stats = {}
            active_connections = 0
            table_count = 0

        # Format created date for display
        created_display = None
        if created:
            try:
                from datetime import datetime
                created_dt = datetime.fromisoformat(created.replace('Z', '+00:00'))
                created_display = created_dt.strftime('%Y-%m-%d %H:%M')
            except:
                created_display = created

        db_info_list.append({
            'name': db_name,
            'size': size,
            'created': created,
            'created_display': created_display,
            'owner': db_stats.get('owner', 'erp'),
            'encoding': db_stats.get('encoding', 'UTF8'),
            'collate': db_stats.get('collate', 'Unknown'),
            'ctype': db_stats.get('ctype', 'Unknown'),
            'allow_connections': db_stats.get('allow_connections', 'Yes'),
            'connection_limit': db_stats.get('connection_limit', -1),
            'size_bytes': db_stats.get('size_bytes', 0),
            'active_connections': active_connections,
            'table_count': table_count,
            'status': 'active',  # Default status
            'has_memory_registry': has_registry,
            'registry_info': filter_result if has_registry else None,
            # New initialization status fields
            'base_module_installed': base_module_installed,
            'init_status': init_status,
            'init_message': init_message,
            'init_icon': init_icon,
            'init_color': init_color
        })

    return db_info_list




async def load_all_templates(template_manager):
    """Load all templates from the templates directory automatically"""
    import os
    from pathlib import Path

    templates_dir = Path("templates")
    if not templates_dir.exists():
        raise Exception(f"Templates directory not found: {templates_dir}")

    # Find all .xml template files recursively
    template_files = []
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.xml'):
                # Get relative path from templates directory
                rel_path = os.path.relpath(os.path.join(root, file), templates_dir)
                template_files.append(rel_path.replace('\\', '/'))  # Normalize path separators

    # Load each template file
    loaded_count = 0
    for template_file in template_files:
        try:
            template_name = template_file.replace('.xml', '').replace('/', '.')
            if not template_manager.template_exists(template_name):
                template_manager.load_template_file(template_file)
                loaded_count += 1
        except Exception as e:
            print(f"Warning: Failed to load template {template_file}: {e}")

    print(f"Loaded {loaded_count} templates from {len(template_files)} template files")
    return loaded_count


@view_router.get("/", response_class=HTMLResponse)
async def database_list_page(request: Request):
    """Database list page with full template system"""
    try:
        template_manager = get_template_manager()

        # Load all templates automatically
        try:
            loaded_count = await load_all_templates(template_manager)
            print(f"DEBUG: Loaded {loaded_count} templates")
            print(f"DEBUG: Available templates: {template_manager.list_templates()}")
        except Exception as e:
            import traceback
            return HTMLResponse(
                content=f"<html><body><h1>Template Loading Error</h1><p>{str(e)}</p><pre>{traceback.format_exc()}</pre></body></html>",
                status_code=500
            )

        # Get database list
        try:
            databases = await get_database_list()
        except Exception as e:
            return HTMLResponse(
                content=f"<html><body><h1>Database List Error</h1><p>{str(e)}</p></body></html>",
                status_code=500
            )

        # Template context for the enhanced modular template
        context = {
            "request": request,
            "title": "Database Management - ERP System",
            "header_title": "Database Management",
            "header_subtitle": "Manage your ERP databases",
            "databases": databases,
            "config": config,
            "is_development_mode": config.is_development_mode,
            "show_header": True
        }

        # Create simple content for the base layout
        database_content = ""
        if databases and len(databases) > 0:
            database_content = f"<p>Found {len(databases)} database(s):</p>"
            for db in databases:
                database_content += f"""
                <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;">
                    <div style="font-weight: bold; font-size: 18px; color: #333;">{db.get('name', 'Unknown')}</div>
                    <div style="color: #666; margin-top: 5px;">
                        Status: {db.get('status', 'Unknown')} |
                        Size: {db.get('size_display', 'Unknown')} |
                        Tables: {db.get('table_count', 0)}
                    </div>
                </div>
                """
        else:
            database_content = """
            <div style="text-align: center; color: #999; padding: 50px;">
                <h3>No databases found</h3>
                <p>No databases are currently available or accessible.</p>
            </div>
            """

        # Update context for base_layout template
        context.update({
            "content": database_content,
            "show_header": True,
            "show_footer": True
        })

        # Debug: List available templates
        available_templates = template_manager.list_templates()

        # Check if our template exists
        enhanced_exists = template_manager.template_exists("database_list_enhanced_modular")
        base_exists = template_manager.template_exists("base_layout")

        # Return debug information for now
        return HTMLResponse(
            content=f"""
            <html><body>
                <h1>Template Debug Information</h1>
                <p><strong>Enhanced template exists:</strong> {enhanced_exists}</p>
                <p><strong>Base layout exists:</strong> {base_exists}</p>
                <p><strong>Available templates:</strong> {available_templates}</p>
                <p><strong>Database count:</strong> {len(databases) if databases else 0}</p>
                <p><strong>Config:</strong> {dict(config.__dict__) if hasattr(config, '__dict__') else str(config)}</p>
            </body></html>
            """,
            status_code=200
        )

        except Exception as e:
            import traceback
            return HTMLResponse(
                content=f"<html><body><h1>Template Rendering Error</h1><p><strong>Error:</strong> {str(e)}</p><p><strong>Traceback:</strong><pre>{traceback.format_exc()}</pre></p><p><strong>Available templates:</strong> {template_manager.list_templates()}</p></body></html>",
                status_code=500
            )

    except Exception as e:
        return HTMLResponse(
            content=f"<html><body><h1>General Error</h1><p>{str(e)}</p></body></html>",
            status_code=500
        )


