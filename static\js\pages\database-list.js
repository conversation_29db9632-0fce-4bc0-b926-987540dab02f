/**
 * Database List Page Initialization
 * Page-specific logic for the database list page including accessibility and error handling
 */

class DatabaseListPage {
    constructor() {
        this.initialized = false;
        this.init();
    }

    init() {
        if (this.initialized) return;
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        console.log('Database List Enhanced (Modular) - Initialized');
        
        // Initialize components
        this.initializeComponents();
        
        // Setup accessibility improvements
        this.setupAccessibility();
        
        // Initialize system statistics
        this.initializeSystemStatistics();
        
        // Setup global functions
        this.setupGlobalFunctions();
        
        this.initialized = true;
    }

    initializeComponents() {
        // Initialize search manager
        if (window.searchManager) {
            window.searchManager.setupSearch();
        }

        // Update last updated time
        this.updateLastUpdatedTime();
    }

    setupAccessibility() {
        // Add accessibility improvements to database cards
        const cards = document.querySelectorAll('[data-dbname]');
        cards.forEach(card => {
            card.setAttribute('role', 'button');
            card.setAttribute('tabindex', '0');
            
            // Add keyboard navigation
            card.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    card.click();
                }
            });
        });
        
        // Add focus management for modals
        const modals = document.querySelectorAll('[id$="Modal"]');
        modals.forEach(modal => {
            modal.setAttribute('role', 'dialog');
            modal.setAttribute('aria-modal', 'true');
        });
    }

    initializeSystemStatistics() {
        // Initialize system statistics
        if (window.databaseOps) {
            window.databaseOps.updateSystemStatistics();
        }
    }

    updateLastUpdatedTime() {
        const updateTime = () => {
            const lastUpdated = document.getElementById('last-updated');
            if (lastUpdated) {
                lastUpdated.textContent = 'Just now';
            }
        };
        updateTime();
    }

    setupGlobalFunctions() {
        // Global functions for system statistics
        window.refreshSystemStats = function() {
            if (window.databaseOps) {
                window.databaseOps.refreshSystemStats();
            }
        };

        window.exportSystemReport = function() {
            if (window.databaseOps) {
                window.databaseOps.exportSystemReport();
            }
        };
    }

    // Show keyboard shortcuts hint in console
    showKeyboardShortcuts() {
        console.log('Keyboard shortcuts:');
        console.log('- Ctrl/Cmd + K: Focus search');
        console.log('- Ctrl/Cmd + N: Create new database');
        console.log('- Escape: Close modals/clear search');
    }

    // Refresh page components
    refresh() {
        // Refresh search manager cards list
        if (window.searchManager) {
            window.searchManager.refreshCardsList();
        }
        
        // Update system statistics
        if (window.databaseOps) {
            window.databaseOps.updateSystemStatistics();
        }
        
        // Re-setup accessibility for new cards
        this.setupAccessibility();
    }

    // Handle page-specific errors
    handleError(error, context = 'page') {
        console.error(`Database List Page error (${context}):`, error);
        
        if (window.Utils && window.Utils.showToast) {
            let message = 'An error occurred on the database list page.';
            
            switch (context) {
                case 'search':
                    message = 'Search functionality encountered an error.';
                    break;
                case 'statistics':
                    message = 'Failed to update system statistics.';
                    break;
                case 'accessibility':
                    message = 'Accessibility setup encountered an error.';
                    break;
                default:
                    message = 'An error occurred. Please refresh the page.';
            }
            
            window.Utils.showToast(message, 'error');
        }
    }

    // Get page state
    getPageState() {
        return {
            initialized: this.initialized,
            searchTerm: window.searchManager ? window.searchManager.getCurrentFilter() : '',
            totalDatabases: document.querySelectorAll('[data-dbname]').length,
            visibleDatabases: document.querySelectorAll('[data-dbname]:not([style*="display: none"])').length
        };
    }
}

// Create global page instance
const databaseListPage = new DatabaseListPage();

// Make page instance available globally
window.databaseListPage = databaseListPage;
