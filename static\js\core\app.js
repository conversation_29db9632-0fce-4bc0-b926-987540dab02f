/**
 * Core Application Initialization
 * Handles global app setup, keyboard shortcuts, and common event handlers
 */

class App {
    constructor() {
        this.initialized = false;
        this.keyboardShortcuts = new Map();
        this.init();
    }

    init() {
        if (this.initialized) return;
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        console.log('ERP Application - Core Initialized');
        
        // Setup global keyboard shortcuts
        this.setupKeyboardShortcuts();
        
        // Setup global error handling
        this.setupErrorHandling();
        
        // Update last updated timestamp
        this.updateLastUpdatedTime();
        
        this.initialized = true;
    }

    setupKeyboardShortcuts() {
        // Register default shortcuts
        this.registerShortcut('Ctrl+K', () => this.focusSearch());
        this.registerShortcut('Cmd+K', () => this.focusSearch());
        this.registerShortcut('Ctrl+N', () => this.createNew());
        this.registerShortcut('Cmd+N', () => this.createNew());
        this.registerShortcut('Escape', () => this.handleEscape());

        // Add global keyboard event listener
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
        
        // Log available shortcuts
        console.log('Keyboard shortcuts:');
        console.log('- Ctrl/Cmd + K: Focus search');
        console.log('- Ctrl/Cmd + N: Create new database');
        console.log('- Escape: Close modals/clear search');
    }

    registerShortcut(combination, callback) {
        this.keyboardShortcuts.set(combination, callback);
    }

    handleKeydown(e) {
        const key = this.getKeyboardCombination(e);
        const callback = this.keyboardShortcuts.get(key);
        
        if (callback) {
            e.preventDefault();
            callback();
        }
    }

    getKeyboardCombination(e) {
        const parts = [];
        
        if (e.ctrlKey) parts.push('Ctrl');
        if (e.metaKey) parts.push('Cmd');
        if (e.altKey) parts.push('Alt');
        if (e.shiftKey) parts.push('Shift');
        
        // Add the main key
        if (e.key !== 'Control' && e.key !== 'Meta' && e.key !== 'Alt' && e.key !== 'Shift') {
            parts.push(e.key);
        }
        
        return parts.join('+');
    }

    focusSearch() {
        const searchInput = document.getElementById('database-search');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    createNew() {
        if (typeof showCreateDatabaseModal === 'function') {
            showCreateDatabaseModal();
        }
    }

    handleEscape() {
        // Close any open modals
        if (window.modalManager && window.modalManager.activeModal) {
            window.modalManager.hideModal(window.modalManager.activeModal);
        }
        
        // Clear search if focused
        const searchInput = document.getElementById('database-search');
        if (searchInput && document.activeElement === searchInput) {
            searchInput.value = '';
            searchInput.blur();
            // Trigger search clear event
            searchInput.dispatchEvent(new Event('input'));
        }
    }

    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (e) => {
            console.error('Page error:', e.error);
            if (window.Utils && window.Utils.showToast) {
                window.Utils.showToast('An error occurred. Please refresh the page.', 'error');
            }
        });
        
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            if (window.Utils && window.Utils.showToast) {
                window.Utils.showToast('A network error occurred. Please try again.', 'error');
            }
        });
    }

    updateLastUpdatedTime() {
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            lastUpdatedElement.textContent = 'Just now';
        }
    }

    // Accessibility improvements
    setupAccessibility() {
        // Add accessibility attributes to database cards
        const cards = document.querySelectorAll('[data-dbname]');
        cards.forEach(card => {
            card.setAttribute('role', 'button');
            card.setAttribute('tabindex', '0');
            
            // Add keyboard navigation
            card.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    card.click();
                }
            });
        });
        
        // Add focus management for modals
        const modals = document.querySelectorAll('[id$="Modal"]');
        modals.forEach(modal => {
            modal.setAttribute('role', 'dialog');
            modal.setAttribute('aria-modal', 'true');
        });
    }
}

// Create global app instance
window.App = new App();
