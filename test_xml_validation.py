#!/usr/bin/env python3
"""
Test script to validate XML template files
"""
import xml.etree.ElementTree as ET
import sys

def validate_xml_file(filepath):
    """Validate an XML file and report any issues"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"Validating: {filepath}")
        print(f"Content length: {len(content)} characters")
        
        # Try to parse the XML
        root = ET.fromstring(content)
        print("✅ XML is valid!")
        
        # Show some info about the structure
        print(f"Root tag: {root.tag}")
        print(f"Number of child elements: {len(root)}")
        
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML Parse Error: {e}")
        print(f"Line: {getattr(e, 'lineno', 'unknown')}, Column: {getattr(e, 'offset', 'unknown')}")

        # Show the problematic line
        lines = content.split('\n')
        if hasattr(e, 'lineno') and e.lineno and e.lineno <= len(lines):
            problematic_line = lines[e.lineno - 1]
            print(f"Problematic line: {problematic_line}")
            if hasattr(e, 'offset') and e.offset:
                print(f"Error position: {' ' * (e.offset - 1)}^")
        
        return False
        
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_xml_validation.py <xml_file>")
        sys.exit(1)
    
    filepath = sys.argv[1]
    success = validate_xml_file(filepath)
    sys.exit(0 if success else 1)
