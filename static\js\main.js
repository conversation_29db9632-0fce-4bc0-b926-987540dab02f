/**
 * Main JavaScript Module Loader
 * Handles loading and initializing all required modules in the correct order
 */

class ModuleLoader {
    constructor() {
        this.loadedModules = new Set();
        this.loadingPromises = new Map();
        this.baseUrl = '/static/js/';
        this.modules = [
            // Core modules (must load first)
            { name: 'utils', path: 'core/utils.js', required: true },
            { name: 'app', path: 'core/app.js', required: true },
            
            // Component modules
            { name: 'modal-manager', path: 'components/modal-manager.js', required: true },
            { name: 'search-filter', path: 'components/search-filter.js', required: true },
            
            // Feature modules
            { name: 'database-operations', path: 'modules/database-operations.js', required: true },
            
            // Page-specific modules (load based on page)
            { name: 'database-list', path: 'pages/database-list.js', required: false, condition: () => this.isDatabaseListPage() }
        ];
        
        this.init();
    }

    async init() {
        console.log('🚀 ERP JavaScript Module Loader - Starting...');
        
        try {
            // Load all required modules
            await this.loadRequiredModules();
            
            // Load conditional modules
            await this.loadConditionalModules();
            
            // Initialize application
            this.initializeApplication();
            
            console.log('✅ ERP JavaScript Module Loader - Complete');
        } catch (error) {
            console.error('❌ ERP JavaScript Module Loader - Failed:', error);
            this.handleLoadError(error);
        }
    }

    async loadRequiredModules() {
        const requiredModules = this.modules.filter(module => module.required);
        
        for (const module of requiredModules) {
            await this.loadModule(module);
        }
    }

    async loadConditionalModules() {
        const conditionalModules = this.modules.filter(module => !module.required && module.condition);
        
        for (const module of conditionalModules) {
            if (module.condition()) {
                await this.loadModule(module);
            }
        }
    }

    async loadModule(module) {
        if (this.loadedModules.has(module.name)) {
            return;
        }

        // Check if already loading
        if (this.loadingPromises.has(module.name)) {
            return this.loadingPromises.get(module.name);
        }

        const loadPromise = this.loadScript(module.path)
            .then(() => {
                this.loadedModules.add(module.name);
                console.log(`📦 Loaded module: ${module.name}`);
            })
            .catch(error => {
                console.error(`❌ Failed to load module ${module.name}:`, error);
                throw error;
            });

        this.loadingPromises.set(module.name, loadPromise);
        return loadPromise;
    }

    loadScript(path) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = this.baseUrl + path;
            script.async = true;
            
            script.onload = () => {
                document.head.removeChild(script);
                resolve();
            };
            
            script.onerror = () => {
                document.head.removeChild(script);
                reject(new Error(`Failed to load script: ${path}`));
            };
            
            document.head.appendChild(script);
        });
    }

    initializeApplication() {
        // Wait a bit for all modules to initialize
        setTimeout(() => {
            // Trigger any final initialization
            if (window.App && window.App.setupAccessibility) {
                window.App.setupAccessibility();
            }
            
            // Show keyboard shortcuts
            if (window.databaseListPage && window.databaseListPage.showKeyboardShortcuts) {
                window.databaseListPage.showKeyboardShortcuts();
            }
            
            console.log('🎉 ERP Application - Ready');
        }, 100);
    }

    handleLoadError(error) {
        // Show user-friendly error message
        const errorMessage = 'Failed to load application modules. Please refresh the page.';
        
        if (window.Utils && window.Utils.showToast) {
            window.Utils.showToast(errorMessage, 'error', 0); // Don't auto-hide
        } else {
            // Fallback to alert if Utils not loaded
            alert(errorMessage);
        }
    }

    // Page detection methods
    isDatabaseListPage() {
        return document.getElementById('database-list') !== null ||
               document.querySelector('[data-dbname]') !== null ||
               window.location.pathname === '/' ||
               window.location.pathname === '/databases';
    }

    // Utility methods for dynamic module loading
    async loadAdditionalModule(name, path) {
        const module = { name, path, required: false };
        await this.loadModule(module);
    }

    isModuleLoaded(name) {
        return this.loadedModules.has(name);
    }

    getLoadedModules() {
        return Array.from(this.loadedModules);
    }

    // Reload all modules (for development)
    async reloadModules() {
        this.loadedModules.clear();
        this.loadingPromises.clear();
        await this.init();
    }
}

// Auto-start module loader when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.moduleLoader = new ModuleLoader();
    });
} else {
    window.moduleLoader = new ModuleLoader();
}

// Make ModuleLoader available globally for debugging
window.ModuleLoader = ModuleLoader;
