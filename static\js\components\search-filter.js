/**
 * Search and Filter Component
 * Handles search functionality, filtering, and debouncing for database cards
 */

class SearchFilterManager {
    constructor() {
        this.searchInput = null;
        this.databaseCards = [];
        this.currentFilter = '';
        this.debounceTimer = null;
        this.debounceDelay = 300; // ms
        
        this.initializeSearch();
    }

    initializeSearch() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupSearch());
        } else {
            this.setupSearch();
        }
    }

    setupSearch() {
        this.searchInput = document.getElementById('database-search');
        this.databaseCards = Array.from(document.querySelectorAll('[data-dbname]'));
        
        if (this.searchInput) {
            // Remove any existing event listeners
            this.searchInput.removeEventListener('input', this.handleSearchInput);
            this.searchInput.removeEventListener('keyup', this.handleSearchInput);
            
            // Add new event listener with debouncing
            this.searchInput.addEventListener('input', (e) => this.handleSearchInput(e));
            this.searchInput.addEventListener('keyup', (e) => this.handleSearchKeyup(e));
            
            // Add clear button functionality
            this.addClearButton();
        }
    }

    handleSearchInput = (event) => {
        const searchTerm = event.target.value;
        
        // Clear previous debounce timer
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        // Set new debounce timer
        this.debounceTimer = setTimeout(() => {
            this.performSearch(searchTerm);
        }, this.debounceDelay);
    }

    handleSearchKeyup = (event) => {
        // Handle escape key to clear search
        if (event.key === 'Escape') {
            this.clearSearch();
        }
    }

    performSearch(searchTerm) {
        this.currentFilter = searchTerm.toLowerCase().trim();
        
        let visibleCount = 0;
        
        this.databaseCards.forEach(card => {
            const dbName = card.dataset.dbname.toLowerCase();
            const shouldShow = this.currentFilter === '' || dbName.includes(this.currentFilter);
            
            if (shouldShow) {
                this.showCard(card);
                visibleCount++;
            } else {
                this.hideCard(card);
            }
        });
        
        this.updateSearchResults(visibleCount);
        this.updateLastUpdated();
    }

    showCard(card) {
        card.style.display = '';
        card.style.opacity = '1';
        card.style.transform = 'scale(1)';
    }

    hideCard(card) {
        card.style.opacity = '0';
        card.style.transform = 'scale(0.95)';
        
        // Hide after animation
        setTimeout(() => {
            if (card.style.opacity === '0') {
                card.style.display = 'none';
            }
        }, 200);
    }

    clearSearch() {
        if (this.searchInput) {
            this.searchInput.value = '';
            this.performSearch('');
            this.searchInput.focus();
        }
    }

    addClearButton() {
        if (!this.searchInput) return;
        
        const searchContainer = this.searchInput.parentElement;
        if (!searchContainer || searchContainer.querySelector('.search-clear-btn')) return;
        
        const clearButton = document.createElement('button');
        clearButton.type = 'button';
        clearButton.className = 'search-clear-btn absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors';
        clearButton.innerHTML = '<i class="fas fa-times text-sm"></i>';
        clearButton.style.display = 'none';
        
        clearButton.addEventListener('click', () => this.clearSearch());
        
        searchContainer.appendChild(clearButton);
        
        // Show/hide clear button based on input content
        this.searchInput.addEventListener('input', (e) => {
            clearButton.style.display = e.target.value ? 'flex' : 'none';
        });
    }

    updateSearchResults(visibleCount) {
        const totalCount = this.databaseCards.length;
        
        // Update search results indicator
        let resultsIndicator = document.getElementById('search-results-indicator');
        
        if (this.currentFilter && visibleCount < totalCount) {
            if (!resultsIndicator) {
                resultsIndicator = document.createElement('div');
                resultsIndicator.id = 'search-results-indicator';
                resultsIndicator.className = 'text-sm text-gray-600 mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg';
                
                const databaseList = document.getElementById('database-list');
                if (databaseList && databaseList.firstChild) {
                    databaseList.insertBefore(resultsIndicator, databaseList.firstChild);
                }
            }
            
            resultsIndicator.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>
                        <i class="fas fa-search mr-2"></i>
                        Showing ${visibleCount} of ${totalCount} databases matching "${this.currentFilter}"
                    </span>
                    <button type="button" 
                            class="text-blue-600 hover:text-blue-800 font-medium" 
                            onclick="searchManager.clearSearch()">
                        Clear filter
                    </button>
                </div>
            `;
        } else if (resultsIndicator) {
            resultsIndicator.remove();
        }
        
        // Show "no results" message if no databases match
        this.updateNoResultsMessage(visibleCount);
    }

    updateNoResultsMessage(visibleCount) {
        let noResultsMessage = document.getElementById('no-search-results');
        
        if (this.currentFilter && visibleCount === 0) {
            if (!noResultsMessage) {
                noResultsMessage = document.createElement('div');
                noResultsMessage.id = 'no-search-results';
                noResultsMessage.className = 'text-center py-12';
                
                const databaseList = document.getElementById('database-list');
                if (databaseList) {
                    databaseList.appendChild(noResultsMessage);
                }
            }
            
            noResultsMessage.innerHTML = `
                <div class="text-gray-500">
                    <i class="fas fa-search text-4xl mb-4 opacity-50"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No databases found</h3>
                    <p class="text-gray-600 mb-4">No databases match your search for "${this.currentFilter}"</p>
                    <button type="button" 
                            class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors" 
                            onclick="searchManager.clearSearch()">
                        <i class="fas fa-times mr-2"></i>
                        Clear search
                    </button>
                </div>
            `;
        } else if (noResultsMessage) {
            noResultsMessage.remove();
        }
    }

    updateLastUpdated() {
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            lastUpdatedElement.textContent = 'Just now';
        }
    }

    // Refresh database cards list (call after dynamic updates)
    refreshCardsList() {
        this.databaseCards = Array.from(document.querySelectorAll('[data-dbname]'));
    }

    // Get current search term
    getCurrentFilter() {
        return this.currentFilter;
    }

    // Set search term programmatically
    setSearchTerm(term) {
        if (this.searchInput) {
            this.searchInput.value = term;
            this.performSearch(term);
        }
    }
}

// Global search manager instance
const searchManager = new SearchFilterManager();

// Global function for backward compatibility
function filterDatabases() {
    const searchInput = document.getElementById('database-search');
    if (searchInput) {
        searchManager.performSearch(searchInput.value);
    }
}

// Export for use in other modules
window.searchManager = searchManager;
