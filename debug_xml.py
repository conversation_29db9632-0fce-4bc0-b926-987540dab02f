#!/usr/bin/env python3
"""
Debug XML file to find the exact issue
"""

def debug_xml_line(filepath, line_num):
    """Debug a specific line in an XML file"""
    with open(filepath, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    if line_num <= len(lines):
        line = lines[line_num - 1]
        print(f"Line {line_num}: {repr(line)}")
        print(f"Length: {len(line)}")
        
        # Show each character with its position
        for i, char in enumerate(line):
            print(f"Pos {i+1:2d}: {repr(char)} (ord: {ord(char)})")
            if i == 34:  # Position 35 (0-based)
                print(f"      ^ This is position 35")

if __name__ == "__main__":
    debug_xml_line("templates/components/create_database_modal.xml", 32)
